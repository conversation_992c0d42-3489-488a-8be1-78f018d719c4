'use client';

import { Line<PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const data = [
  { month: 'يناير', users: 400, experts: 240 },
  { month: 'فبراير', users: 300, experts: 139 },
  { month: 'مارس', users: 200, experts: 980 },
  { month: 'أبريل', users: 278, experts: 390 },
  { month: 'مايو', users: 189, experts: 480 },
  { month: 'يونيو', users: 239, experts: 380 },
  { month: 'يوليو', users: 349, experts: 430 },
];

export function UserGrowthChart() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          نمو المستخدمين والخبراء
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          إحصائيات النمو الشهري للمنصة
        </p>
      </div>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-gray-200 dark:stroke-gray-700" />
            <XAxis 
              dataKey="month" 
              className="text-gray-600 dark:text-gray-400"
              tick={{ fontSize: 12 }}
            />
            <YAxis 
              className="text-gray-600 dark:text-gray-400"
              tick={{ fontSize: 12 }}
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: 'rgb(31 41 55)',
                border: '1px solid rgb(75 85 99)',
                borderRadius: '0.5rem',
                color: 'rgb(243 244 246)',
              }}
            />
            <Line 
              type="monotone" 
              dataKey="users" 
              stroke="#3b82f6" 
              strokeWidth={2}
              name="المستخدمين"
            />
            <Line 
              type="monotone" 
              dataKey="experts" 
              stroke="#10b981" 
              strokeWidth={2}
              name="الخبراء"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
