const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require('path');

// Get the default Metro config
const defaultConfig = getDefaultConfig(__dirname);

// Custom configuration for monorepo
const config = {
  watchFolders: [
    // Include the root directory and packages
    path.resolve(__dirname, '../..'),
  ],
  resolver: {
    // Support for monorepo packages
    nodeModulesPaths: [
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules'),
    ],
    // Additional file extensions
    sourceExts: [...defaultConfig.resolver.sourceExts, 'svg'],
    // Asset extensions
    assetExts: [...defaultConfig.resolver.assetExts.filter(ext => ext !== 'svg')],
  },
  transformer: {
    // SVG transformer
    babelTransformerPath: require.resolve('react-native-svg-transformer'),
  },
};

module.exports = mergeConfig(defaultConfig, config);
