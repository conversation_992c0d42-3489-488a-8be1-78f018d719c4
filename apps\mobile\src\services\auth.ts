import { apiRequest } from './api';
import { User, UserRole } from '@freela/types';

// Types
interface LoginRequest {
  email: string;
  password: string;
}

interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: UserR<PERSON>;
}

interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
  user: User;
}

// Auth service
export const authService = {
  // Login
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    return apiRequest.post('/auth/login', credentials);
  },

  // Register
  register: async (userData: RegisterRequest): Promise<AuthResponse> => {
    return apiRequest.post('/auth/register', userData);
  },

  // Logout
  logout: async (): Promise<void> => {
    return apiRequest.post('/auth/logout');
  },

  // Refresh token
  refreshToken: async (refreshToken: string): Promise<RefreshTokenResponse> => {
    return apiRequest.post('/auth/refresh', { refreshToken });
  },

  // Get current user
  getCurrentUser: async (): Promise<User> => {
    return apiRequest.get('/auth/me');
  },

  // Forgot password
  forgotPassword: async (email: string): Promise<{ message: string }> => {
    return apiRequest.post('/auth/forgot-password', { email });
  },

  // Reset password
  resetPassword: async (token: string, password: string): Promise<{ message: string }> => {
    return apiRequest.post('/auth/reset-password', { token, password });
  },

  // Verify email
  verifyEmail: async (token: string): Promise<{ message: string }> => {
    return apiRequest.post('/auth/verify-email', { token });
  },

  // Resend verification email
  resendVerificationEmail: async (email: string): Promise<{ message: string }> => {
    return apiRequest.post('/auth/resend-verification', { email });
  },

  // Change password
  changePassword: async (currentPassword: string, newPassword: string): Promise<{ message: string }> => {
    return apiRequest.post('/auth/change-password', {
      currentPassword,
      newPassword,
    });
  },

  // Update profile
  updateProfile: async (userData: Partial<User>): Promise<User> => {
    return apiRequest.patch('/auth/profile', userData);
  },

  // Delete account
  deleteAccount: async (password: string): Promise<{ message: string }> => {
    return apiRequest.delete('/auth/account', {
      data: { password },
    });
  },
};
