import React, { useEffect } from 'react';
import { Status<PERSON>ar, I18nManager } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import Toast from 'react-native-toast-message';

// Navigation
import RootNavigator from './navigation/RootNavigator';

// Services
import { initializeI18n } from './services/i18n';
import { initializeApp } from './services/app';

// Store
import { useAuthStore } from './store/authStore';
import { useAppStore } from './store/appStore';

// Utils
import { setupAxiosInterceptors } from './services/api';

// Styles
import { ThemeProvider } from './contexts/ThemeContext';
import { toastConfig } from './utils/toast';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

const App: React.FC = () => {
  const { isAuthenticated, initializeAuth } = useAuthStore();
  const { isAppReady, initializeAppState } = useAppStore();

  useEffect(() => {
    const initApp = async () => {
      try {
        // Initialize i18n
        await initializeI18n();
        
        // Force RTL layout for Arabic
        if (!I18nManager.isRTL) {
          I18nManager.forceRTL(true);
          // Note: In production, you might want to restart the app here
          // RNRestart.Restart();
        }

        // Initialize app services
        await initializeApp();
        
        // Setup API interceptors
        setupAxiosInterceptors();
        
        // Initialize authentication state
        await initializeAuth();
        
        // Mark app as ready
        initializeAppState();
      } catch (error) {
        console.error('Failed to initialize app:', error);
        // Handle initialization error
      }
    };

    initApp();
  }, [initializeAuth, initializeAppState]);

  if (!isAppReady) {
    // You can return a splash screen component here
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <QueryClientProvider client={queryClient}>
        <SafeAreaProvider>
          <ThemeProvider>
            <NavigationContainer>
              <StatusBar
                barStyle="dark-content"
                backgroundColor="transparent"
                translucent
              />
              <RootNavigator />
              <Toast config={toastConfig} />
            </NavigationContainer>
          </ThemeProvider>
        </SafeAreaProvider>
      </QueryClientProvider>
    </GestureHandlerRootView>
  );
};

export default App;
