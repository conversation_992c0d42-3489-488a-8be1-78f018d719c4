'use client';

import { CheckCircleIcon, XCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

const activities = [
  {
    id: 1,
    type: 'user_registration',
    message: 'مستخدم جديد سجل في المنصة',
    user: 'أحم<PERSON> محمد',
    time: 'منذ 5 دقائق',
    status: 'success',
  },
  {
    id: 2,
    type: 'service_published',
    message: 'خدمة جديدة تم نشرها',
    user: 'فاطمة أحمد',
    time: 'منذ 15 دقيقة',
    status: 'success',
  },
  {
    id: 3,
    type: 'payment_failed',
    message: 'فشل في عملية دفع',
    user: 'محمد علي',
    time: 'منذ 30 دقيقة',
    status: 'error',
  },
  {
    id: 4,
    type: 'booking_pending',
    message: 'حجز جديد في انتظار الموافقة',
    user: 'سارة خالد',
    time: 'منذ ساعة',
    status: 'warning',
  },
  {
    id: 5,
    type: 'expert_verified',
    message: 'تم التحقق من خبير جديد',
    user: 'عمر حسن',
    time: 'منذ ساعتين',
    status: 'success',
  },
];

const statusIcons = {
  success: CheckCircleIcon,
  error: XCircleIcon,
  warning: ExclamationTriangleIcon,
};

const statusColors = {
  success: 'text-green-500',
  error: 'text-red-500',
  warning: 'text-yellow-500',
};

export function RecentActivity() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          النشاطات الأخيرة
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          آخر الأحداث والنشاطات على المنصة
        </p>
      </div>
      
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {activities.map((activity) => {
          const Icon = statusIcons[activity.status];
          return (
            <div key={activity.id} className="px-6 py-4">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Icon className={`h-5 w-5 ${statusColors[activity.status]}`} />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {activity.message}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    بواسطة {activity.user} • {activity.time}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center">
        <button className="text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500">
          عرض جميع النشاطات
        </button>
      </div>
    </div>
  );
}
