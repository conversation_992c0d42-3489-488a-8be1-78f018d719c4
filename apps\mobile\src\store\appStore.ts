import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface AppState {
  // App state
  isAppReady: boolean;
  language: 'ar' | 'en';
  theme: 'light' | 'dark';
  isOnline: boolean;
  
  // Preferences
  notifications: {
    push: boolean;
    email: boolean;
    sms: boolean;
    marketing: boolean;
  };
  
  // Cache
  lastSyncTime: number | null;
  
  // Actions
  initializeAppState: () => void;
  setLanguage: (language: 'ar' | 'en') => void;
  setTheme: (theme: 'light' | 'dark') => void;
  setOnlineStatus: (isOnline: boolean) => void;
  updateNotificationSettings: (settings: Partial<AppState['notifications']>) => void;
  updateLastSyncTime: () => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      isAppReady: false,
      language: 'ar', // Default to Arabic
      theme: 'light',
      isOnline: true,
      notifications: {
        push: true,
        email: true,
        sms: false,
        marketing: false,
      },
      lastSyncTime: null,

      // Actions
      initializeAppState: () => {
        set({ isAppReady: true });
      },

      setLanguage: (language: 'ar' | 'en') => {
        set({ language });
      },

      setTheme: (theme: 'light' | 'dark') => {
        set({ theme });
      },

      setOnlineStatus: (isOnline: boolean) => {
        set({ isOnline });
      },

      updateNotificationSettings: (settings: Partial<AppState['notifications']>) => {
        set((state) => ({
          notifications: {
            ...state.notifications,
            ...settings,
          },
        }));
      },

      updateLastSyncTime: () => {
        set({ lastSyncTime: Date.now() });
      },
    }),
    {
      name: 'freela-app-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        language: state.language,
        theme: state.theme,
        notifications: state.notifications,
        lastSyncTime: state.lastSyncTime,
      }),
    }
  )
);
