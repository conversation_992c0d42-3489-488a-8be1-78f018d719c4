{"name": "@freela/expert-dashboard", "version": "1.0.0", "description": "Freela Syria Expert Dashboard - Next.js", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@freela/types": "file:../../packages/types", "@freela/utils": "file:../../packages/utils", "@freela/i18n": "file:../../packages/i18n", "next": "14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "14.0.3", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.10.7", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/aspect-ratio": "^0.4.2", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "axios": "^1.6.2", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1", "next-themes": "^0.2.1", "framer-motion": "^10.16.5", "react-dropzone": "^14.2.3", "react-calendar": "^4.6.0", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "next-i18next": "^15.2.0"}, "devDependencies": {"@types/node": "^20.9.2", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^9.0.0", "prettier": "^3.1.0", "typescript": "^5.3.2", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}