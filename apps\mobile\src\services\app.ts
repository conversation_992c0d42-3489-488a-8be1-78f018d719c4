import NetInfo from '@react-native-community/netinfo';
import { useAppStore } from '../store/appStore';

// Initialize app services
export const initializeApp = async (): Promise<void> => {
  try {
    // Setup network monitoring
    setupNetworkMonitoring();
    
    // Initialize other services here
    console.log('App services initialized successfully');
  } catch (error) {
    console.error('Failed to initialize app services:', error);
    throw error;
  }
};

// Setup network monitoring
const setupNetworkMonitoring = (): void => {
  NetInfo.addEventListener(state => {
    const { setOnlineStatus } = useAppStore.getState();
    setOnlineStatus(state.isConnected ?? false);
  });
};
