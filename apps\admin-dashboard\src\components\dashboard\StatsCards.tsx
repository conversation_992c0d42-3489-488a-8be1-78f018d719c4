'use client';

import { UsersIcon, UserGroupIcon, BriefcaseIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline';

const stats = [
  {
    name: 'إجمالي المستخدمين',
    value: '2,847',
    change: '+12%',
    changeType: 'increase',
    icon: UsersIcon,
  },
  {
    name: 'الخبراء النشطين',
    value: '1,234',
    change: '+8%',
    changeType: 'increase',
    icon: UserGroupIcon,
  },
  {
    name: 'الخدمات المنشورة',
    value: '5,678',
    change: '+15%',
    changeType: 'increase',
    icon: BriefcaseIcon,
  },
  {
    name: 'الإيرادات الشهرية',
    value: '$45,231',
    change: '+23%',
    changeType: 'increase',
    icon: CurrencyDollarIcon,
  },
];

export function StatsCards() {
  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <div
          key={stat.name}
          className="relative overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:px-6 sm:py-6"
        >
          <dt>
            <div className="absolute rounded-md bg-primary-500 p-3">
              <stat.icon className="h-6 w-6 text-white" aria-hidden="true" />
            </div>
            <p className="mr-16 rtl:mr-0 rtl:ml-16 truncate text-sm font-medium text-gray-500 dark:text-gray-400">
              {stat.name}
            </p>
          </dt>
          <dd className="mr-16 rtl:mr-0 rtl:ml-16 flex items-baseline pb-6 sm:pb-7">
            <p className="text-2xl font-semibold text-gray-900 dark:text-white">
              {stat.value}
            </p>
            <p
              className={`mr-2 rtl:mr-0 rtl:ml-2 flex items-baseline text-sm font-semibold ${
                stat.changeType === 'increase'
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-red-600 dark:text-red-400'
              }`}
            >
              {stat.change}
            </p>
            <div className="absolute inset-x-0 bottom-0 bg-gray-50 dark:bg-gray-700 px-4 py-4 sm:px-6">
              <div className="text-sm">
                <span className="font-medium text-primary-600 dark:text-primary-400">
                  مقارنة بالشهر الماضي
                </span>
              </div>
            </div>
          </dd>
        </div>
      ))}
    </div>
  );
}
