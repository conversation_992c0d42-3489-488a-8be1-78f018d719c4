'use client';

import { StatsCards } from '@/components/dashboard/StatsCards';
import { RecentActivity } from '@/components/dashboard/RecentActivity';
import { UserGrowthChart } from '@/components/dashboard/UserGrowthChart';
import { RevenueChart } from '@/components/dashboard/RevenueChart';

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          لوحة التحكم الرئيسية
        </h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          نظرة عامة على منصة فريلا سوريا
        </p>
      </div>

      {/* Stats cards */}
      <StatsCards />

      {/* Charts grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <UserGrowthChart />
        <RevenueChart />
      </div>

      {/* Recent activity */}
      <RecentActivity />
    </div>
  );
}
