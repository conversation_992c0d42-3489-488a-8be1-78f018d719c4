'use client';

import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const data = [
  { month: 'يناير', revenue: 4000, commissions: 400 },
  { month: 'فبراير', revenue: 3000, commissions: 300 },
  { month: 'مارس', revenue: 2000, commissions: 200 },
  { month: 'أبريل', revenue: 2780, commissions: 278 },
  { month: 'مايو', revenue: 1890, commissions: 189 },
  { month: 'يونيو', revenue: 2390, commissions: 239 },
  { month: 'يوليو', revenue: 3490, commissions: 349 },
];

export function RevenueChart() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          الإيرادات والعمولات
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          إحصائيات الإيرادات الشهرية بالدولار الأمريكي
        </p>
      </div>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-gray-200 dark:stroke-gray-700" />
            <XAxis 
              dataKey="month" 
              className="text-gray-600 dark:text-gray-400"
              tick={{ fontSize: 12 }}
            />
            <YAxis 
              className="text-gray-600 dark:text-gray-400"
              tick={{ fontSize: 12 }}
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: 'rgb(31 41 55)',
                border: '1px solid rgb(75 85 99)',
                borderRadius: '0.5rem',
                color: 'rgb(243 244 246)',
              }}
              formatter={(value: number) => [`$${value}`, '']}
            />
            <Bar 
              dataKey="revenue" 
              fill="#3b82f6" 
              name="إجمالي الإيرادات"
              radius={[4, 4, 0, 0]}
            />
            <Bar 
              dataKey="commissions" 
              fill="#10b981" 
              name="عمولات المنصة"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
