module.exports = {
  i18n: {
    defaultLocale: 'ar',
    locales: ['ar', 'en'],
    localeDetection: false,
  },
  fallbackLng: {
    default: ['ar'],
  },
  debug: process.env.NODE_ENV === 'development',
  reloadOnPrerender: process.env.NODE_ENV === 'development',
  
  // Namespace configuration
  ns: ['common', 'dashboard', 'users', 'experts', 'services', 'bookings', 'payments'],
  defaultNS: 'common',
  
  // Load path for translation files
  localePath: './public/locales',
  
  // Interpolation settings for Arabic
  interpolation: {
    escapeValue: false,
    format: function(value, format, lng) {
      if (format === 'currency') {
        return new Intl.NumberFormat(lng === 'ar' ? 'ar-SY' : 'en-US', {
          style: 'currency',
          currency: 'USD',
        }).format(value);
      }
      if (format === 'number') {
        return new Intl.NumberFormat(lng === 'ar' ? 'ar-SY' : 'en-US').format(value);
      }
      return value;
    },
  },
  
  // React specific settings
  react: {
    useSuspense: false,
  },
};
