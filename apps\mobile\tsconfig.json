{"extends": "../../tsconfig.json", "compilerOptions": {"target": "esnext", "lib": ["es2017"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/navigation/*": ["./src/navigation/*"], "@/services/*": ["./src/services/*"], "@/store/*": ["./src/store/*"], "@/utils/*": ["./src/utils/*"], "@/hooks/*": ["./src/hooks/*"], "@/constants/*": ["./src/constants/*"], "@/types/*": ["./src/types/*"], "@/assets/*": ["./src/assets/*"]}}, "include": ["src/**/*", "index.js", "__tests__/**/*"], "exclude": ["node_modules", "android", "ios"]}