'use client';

import { EarningsOverview } from '@/components/dashboard/EarningsOverview';
import { BookingStats } from '@/components/dashboard/BookingStats';
import { ServicePerformance } from '@/components/dashboard/ServicePerformance';
import { RecentBookings } from '@/components/dashboard/RecentBookings';
import { QuickActions } from '@/components/dashboard/QuickActions';

export default function ExpertDashboardPage() {
  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          مرحباً بك، أحمد محمد
        </h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          إليك نظرة عامة على أداءك وأنشطتك الأخيرة
        </p>
      </div>

      {/* Quick actions */}
      <QuickActions />

      {/* Stats grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <EarningsOverview />
        <BookingStats />
      </div>

      {/* Service performance */}
      <ServicePerformance />

      {/* Recent bookings */}
      <RecentBookings />
    </div>
  );
}
