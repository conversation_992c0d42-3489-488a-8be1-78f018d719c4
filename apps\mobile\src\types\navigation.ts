import { NavigatorScreenParams } from '@react-navigation/native';

// Root Stack
export type RootStackParamList = {
  Onboarding: undefined;
  Auth: NavigatorScreenParams<AuthStackParamList>;
  Main: NavigatorScreenParams<MainStackParamList>;
};

// Auth Stack
export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  RoleSelection: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
  };
  ForgotPassword: undefined;
  VerifyEmail: {
    email: string;
  };
  ResetPassword: {
    token: string;
  };
};

// Main Stack
export type MainStackParamList = {
  Tabs: NavigatorScreenParams<MainTabParamList>;
  ServiceDetails: {
    serviceId: string;
  };
  ExpertProfile: {
    expertId: string;
  };
  BookingDetails: {
    bookingId: string;
  };
  ChatConversation: {
    conversationId: string;
  };
  EditProfile: undefined;
  Settings: undefined;
  Notifications: undefined;
};

// Main Tabs (Client)
export type MainTabParamList = {
  Home: undefined;
  Search: undefined;
  Bookings: undefined;
  Chat: undefined;
  Profile: undefined;
} | {
  // Expert tabs
  Dashboard: undefined;
  Services: undefined;
  Bookings: undefined;
  Chat: undefined;
  Earnings: undefined;
};

// Screen props helper types
export type RootStackScreenProps<T extends keyof RootStackParamList> = {
  navigation: any;
  route: {
    params: RootStackParamList[T];
  };
};

export type AuthStackScreenProps<T extends keyof AuthStackParamList> = {
  navigation: any;
  route: {
    params: AuthStackParamList[T];
  };
};

export type MainStackScreenProps<T extends keyof MainStackParamList> = {
  navigation: any;
  route: {
    params: MainStackParamList[T];
  };
};
