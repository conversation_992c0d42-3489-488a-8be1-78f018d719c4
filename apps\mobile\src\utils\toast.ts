import Toast, { ToastConfig } from 'react-native-toast-message';

// Custom toast configuration
export const toastConfig: ToastConfig = {
  success: ({ text1, text2 }) => (
    <div style={{
      backgroundColor: '#10B981',
      padding: 16,
      borderRadius: 8,
      margin: 16,
    }}>
      <div style={{ color: 'white', fontWeight: 'bold' }}>{text1}</div>
      {text2 && <div style={{ color: 'white', fontSize: 14 }}>{text2}</div>}
    </div>
  ),
  error: ({ text1, text2 }) => (
    <div style={{
      backgroundColor: '#EF4444',
      padding: 16,
      borderRadius: 8,
      margin: 16,
    }}>
      <div style={{ color: 'white', fontWeight: 'bold' }}>{text1}</div>
      {text2 && <div style={{ color: 'white', fontSize: 14 }}>{text2}</div>}
    </div>
  ),
  info: ({ text1, text2 }) => (
    <div style={{
      backgroundColor: '#3B82F6',
      padding: 16,
      borderRadius: 8,
      margin: 16,
    }}>
      <div style={{ color: 'white', fontWeight: 'bold' }}>{text1}</div>
      {text2 && <div style={{ color: 'white', fontSize: 14 }}>{text2}</div>}
    </div>
  ),
};

// Toast helper functions
export const showToast = {
  success: (message: string, description?: string) => {
    Toast.show({
      type: 'success',
      text1: message,
      text2: description,
      position: 'top',
      visibilityTime: 3000,
    });
  },
  
  error: (message: string, description?: string) => {
    Toast.show({
      type: 'error',
      text1: message,
      text2: description,
      position: 'top',
      visibilityTime: 4000,
    });
  },
  
  info: (message: string, description?: string) => {
    Toast.show({
      type: 'info',
      text1: message,
      text2: description,
      position: 'top',
      visibilityTime: 3000,
    });
  },
};
