import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from './providers';
import './globals.css';

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
});

export const metadata: Metadata = {
  title: 'Freela Syria - Expert Dashboard',
  description: 'Expert dashboard for Freela Syria marketplace',
  keywords: ['freelance', 'syria', 'expert', 'dashboard'],
  authors: [{ name: 'Freela Syria Team' }],
  viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className={`${inter.variable} font-arabic antialiased`}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
