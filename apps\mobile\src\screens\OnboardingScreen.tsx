import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Store
import { useAppStore } from '../store/appStore';

// Theme
import { useTheme } from '../contexts/ThemeContext';

// Components
import { Button } from '../components/common/Button';

const { width } = Dimensions.get('window');

interface OnboardingSlide {
  id: number;
  title: string;
  description: string;
  icon: string;
}

const slides: OnboardingSlide[] = [
  {
    id: 1,
    title: 'مرحباً بك في فريلا سوريا',
    description: 'منصة العمل الحر الأولى المخصصة للخبراء السوريين والمدعومة بالذكاء الاصطناعي',
    icon: 'waving-hand',
  },
  {
    id: 2,
    title: 'اعثر على الخبراء المناسبين',
    description: 'استخدم الذكاء الاصطناعي للعثور على أفضل الخبراء لمشروعك بسرعة ودقة',
    icon: 'search',
  },
  {
    id: 3,
    title: 'تواصل بسهولة',
    description: 'نظام محادثة متطور مع ترجمة فورية ودعم للغة العربية',
    icon: 'chat',
  },
  {
    id: 4,
    title: 'مدفوعات آمنة',
    description: 'نظام دفع آمن ومرن يدعم العملات المحلية والعالمية',
    icon: 'security',
  },
];

const OnboardingScreen: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const { colors } = useTheme();
  const { setOnboardingCompleted } = useAppStore();

  const handleNext = () => {
    if (currentSlide < slides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    } else {
      setOnboardingCompleted();
    }
  };

  const handleSkip = () => {
    setOnboardingCompleted();
  };

  const slide = slides[currentSlide];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Skip button */}
      <TouchableOpacity
        style={styles.skipButton}
        onPress={handleSkip}
      >
        <Text style={[styles.skipText, { color: colors.textSecondary }]}>
          تخطي
        </Text>
      </TouchableOpacity>

      {/* Content */}
      <View style={styles.content}>
        {/* Icon */}
        <View style={[styles.iconContainer, { backgroundColor: colors.primaryLight }]}>
          <Icon
            name={slide.icon}
            size={80}
            color={colors.primary}
          />
        </View>

        {/* Title */}
        <Text style={[styles.title, { color: colors.text }]}>
          {slide.title}
        </Text>

        {/* Description */}
        <Text style={[styles.description, { color: colors.textSecondary }]}>
          {slide.description}
        </Text>
      </View>

      {/* Bottom section */}
      <View style={styles.bottomSection}>
        {/* Pagination dots */}
        <View style={styles.pagination}>
          {slides.map((_, index) => (
            <View
              key={index}
              style={[
                styles.dot,
                {
                  backgroundColor: index === currentSlide ? colors.primary : colors.border,
                },
              ]}
            />
          ))}
        </View>

        {/* Next button */}
        <Button
          title={currentSlide === slides.length - 1 ? 'ابدأ الآن' : 'التالي'}
          onPress={handleNext}
          style={styles.nextButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  skipButton: {
    alignSelf: 'flex-start',
    padding: 16,
    marginTop: 8,
  },
  skipText: {
    fontSize: 16,
    fontFamily: 'Cairo-Regular',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  iconContainer: {
    width: 160,
    height: 160,
    borderRadius: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    fontFamily: 'Cairo-Bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: 'Cairo-Regular',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 16,
  },
  bottomSection: {
    paddingHorizontal: 32,
    paddingBottom: 32,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  nextButton: {
    marginTop: 16,
  },
});

export default OnboardingScreen;
